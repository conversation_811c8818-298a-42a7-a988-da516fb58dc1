using Common;
using Managers;
using System;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace UnitParts.Orders
{
    /// <summary>
    /// Advanced movement order that combines movement with enemy detection and engagement.
    /// The unit moves toward a target position while continuously scanning for enemies within
    /// a detection radius. When enemies are detected, movement is interrupted to engage them,
    /// then resumes after enemies are eliminated or move out of range.
    /// </summary>
    /// <remarks>
    /// Behavior:
    /// - Moves toward target position using pathfinding
    /// - Continuously scans for enemies within detection radius every 0.2 seconds
    /// - Interrupts movement to attack detected enemies using optimal positioning
    /// - Resumes movement to original target after enemies are eliminated or out of range
    /// - Uses complex order chain with EnemyDetectionDecorator and attack sub-orders
    ///
    /// Performance: Pooled object, moderate overhead due to continuous enemy detection
    /// Completion: When unit reaches target position and no enemies are in detection range
    /// Interruption: Can be cancelled, will stop movement and combat, clean up sub-orders
    ///
    /// Usage Examples:
    /// - Basic attack move: var order = AttackMoveOrder.Get(); order.Initialize(unit, targetPos, 6f, unit);
    /// - Long range patrol: order.Initialize(unit, patrolPoint, 10f, unit);
    /// - Typically used for aggressive unit movement in combat scenarios
    /// </remarks>
    [Serializable]
    public class AttackMoveOrder : BaseOrder
    {
        /// <summary>
        /// The final destination position in world coordinates where the unit should move.
        /// Movement will be interrupted for combat but will resume toward this position.
        /// </summary>
        public Vector3 TargetPosition;

        /// <summary>
        /// The radius in world units for enemy detection during movement.
        /// Enemies within this radius will trigger combat interruption.
        /// Range: 1.0-20.0 units recommended. Larger values increase detection overhead.
        /// </summary>
        public float DetectionRadius;

        /// <summary>
        /// The unit controller that will execute this order.
        /// Must have valid MovementController and GunController for full functionality.
        /// </summary>
        public UnitController UnitController;

        private IBaseOrder _currentSubOrder;
        private bool _isMoving = true;
        private Vector3 _lastPosition;

        public AttackMoveOrder Initialize(MonoBehaviour issuer, Vector3 targetPosition, float detectionRadius,
            UnitController unitController, Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            TargetPosition = targetPosition;
            DetectionRadius = detectionRadius;
            UnitController = unitController;

            return this;
        }

        public override void Dispose()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.AttackMoveOrderPool?.Return(this);
        }

        public static new AttackMoveOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.AttackMoveOrderPool?.Get() ?? new AttackMoveOrder();
        }

        public override void Execute()
        {
            _lastPosition = UnitController.Position;
            StartMovementWithDetection();
        }

        private void StartMovementWithDetection()
        {
            // Create move order to target position (static position)
            var moveOrder = OrderUtils.CreateMoveOrder(UnitController, TargetPosition);
            moveOrder.OnComplete += () => InvokeOnComplete(); // Complete when destination reached

            // Wrap with enemy detection
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            var detectionDecorator = orderPoolManager?.CreateEnemyDetectionDecorator(moveOrder) ?? new EnemyDetectionDecorator(moveOrder);
            detectionDecorator.detectionRadius = DetectionRadius;
            detectionDecorator.onEnemyDetected = OnEnemyDetected;
            detectionDecorator.onNoEnemiesDetected = OnNoEnemiesDetected;

            _currentSubOrder = detectionDecorator;
            _currentSubOrder.Execute();
            _isMoving = true;
        }

        private void OnEnemyDetected(ITeamDamageable enemy)
        {
            if (!_isMoving)
            {
                return;
            }

            var enemyName = (enemy as MonoBehaviour)?.name ?? "Unknown Enemy";
            Debug.Log($"[AttackMove] Enemy detected: {enemyName}, switching to attack mode");
            _isMoving = false;
            _lastPosition = UnitController.Position; // Remember where we stopped
            _currentSubOrder?.Cancel();

            // Create attack order
            var attackOrder = CreateAttackOrder(enemy);
            _currentSubOrder = attackOrder;
            _currentSubOrder.Execute();
        }

        private void OnNoEnemiesDetected()
        {
            if (_isMoving)
            {
                return;
            }

            Debug.Log("[AttackMove] No enemies detected, resuming movement");
            // Resume movement from current position to target
            ResumeMovement();
        }

        private void ResumeMovement()
        {
            if (_isMoving)
            {
                Debug.Log("[AttackMove] Already moving, ignoring resume request");
                return;
            }

            Debug.Log("[AttackMove] Resuming movement to target");
            _isMoving = true;
            _currentSubOrder?.Cancel();

            // Check if we've reached the target
            var distanceToTarget = Vector3.Distance(UnitController.Position, TargetPosition);
            if (distanceToTarget <= 1f) // Close enough to target
            {
                Debug.Log("[AttackMove] Reached target, completing order");
                InvokeOnComplete();
                return;
            }

            // Resume movement from current position to target
            StartMovementWithDetection();
        }

        private IBaseOrder CreateAttackOrder(ITeamDamageable target)
        {
            // Calculate optimal attack position to maintain proper distance for gun targeting
            var optimalPosition = CalculateOptimalAttackPosition(target);
            var moveOrder = OrderUtils.CreateMoveOrder(UnitController, optimalPosition);

            // Add gun targeting so the unit shoots while moving toward the optimal position
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            var gunTargetDecorator = orderPoolManager?.CreateGunTargetDecorator(moveOrder) ?? new GunTargetDecorator(moveOrder);
            gunTargetDecorator.target = target;

            // Add a timed decorator to prevent getting stuck
            var timedDecorator = orderPoolManager?.CreateTimedDecorator(gunTargetDecorator) ?? new TimedDecorator(gunTargetDecorator);
            timedDecorator.timeLimit = 3f;

            // Add condition to stop attacking when target is dead or out of detection range
            var attackConditionDecorator = orderPoolManager?.CreateConditionalDecorator(timedDecorator) ?? new ConditionalDecorator(timedDecorator);
            attackConditionDecorator.condition = () =>
            {
                try
                {
                    if (target == null || target.IsDestroyed)
                    {
                        return true;
                    }

                    var distanceToTarget = Vector3.Distance(UnitController.Position, target.Position);
                    return distanceToTarget > DetectionRadius * 1.2f; // Small buffer to prevent oscillation
                }
                catch (NullReferenceException)
                {
                    // Target was destroyed or became invalid
                    return true;
                }
            };
            attackConditionDecorator.onCondition = () => ResumeMovement();

            // Add completion and timeout callbacks to resume movement
            attackConditionDecorator.OnComplete += () => ResumeMovement();
            timedDecorator.onTimeout += () => ResumeMovement();

            return attackConditionDecorator;
        }

        /// <summary>
        ///     Calculate optimal attack position that maintains proper distance from target for effective gun targeting
        /// </summary>
        private Vector3 CalculateOptimalAttackPosition(ITeamDamageable target)
        {
            if (UnitController.gunController?.gun == null)
            {
                // No gun, move directly to target
                return target.Position;
            }

            var gunRange = UnitController.gunController.gun.range;
            var currentDistance = Vector3.Distance(UnitController.Position, target.Position);

            // Define optimal distance range
            var minDistance = gunRange * 0.2f; // Minimum distance to avoid getting too close
            var optimalDistance = gunRange * 0.8f; // Optimal distance for targeting

            // If already at good distance, stay put
            if (currentDistance >= minDistance && currentDistance <= optimalDistance)
            {
                return UnitController.Position;
            }

            // Calculate direction from target to unit
            var directionToUnit = (UnitController.Position - target.Position).normalized;

            // If unit is too close or direction is invalid, use a default direction
            if (directionToUnit.magnitude < 0.1f)
            {
                // Try different directions to find a good position
                var angles = new[] { 0f, 90f, 180f, 270f, 45f, 135f, 225f, 315f };
                foreach (var angle in angles)
                {
                    var direction = new Vector3(Mathf.Cos(angle * Mathf.Deg2Rad), Mathf.Sin(angle * Mathf.Deg2Rad), 0);
                    var testPosition = target.Position + (direction * optimalDistance);

                    // Use the first direction that doesn't overlap with the target
                    if (Vector3.Distance(testPosition, target.Position) >= minDistance)
                    {
                        directionToUnit = direction;
                        break;
                    }
                }

                // Fallback if no good direction found
                if (directionToUnit.magnitude < 0.1f)
                {
                    directionToUnit = Vector3.right;
                }
            }

            // Calculate optimal position
            var optimalPosition = target.Position + (directionToUnit * optimalDistance);

            return optimalPosition;
        }

        public override void Cancel()
        {
            _currentSubOrder?.Cancel();
            InvokeOnCancel();
        }
    }
}

using UnitParts.Interfaces;
using UnitParts.Orders;
using UnitParts.Orders.Decorators;

namespace Managers
{
    /// <summary>
    ///     Interface for OrderPoolManager to enable dependency injection
    /// </summary>
    public interface IOrderPoolManager
    {
        /// <summary>
        ///     Pool for move orders
        /// </summary>
        ObjectPool<MoveOrder> MoverOrderPool { get; }

        /// <summary>
        ///     Pool for attack move orders
        /// </summary>
        ObjectPool<AttackMoveOrder> AttackMoveOrderPool { get; }

        /// <summary>
        ///     Pool for stay still orders
        /// </summary>
        ObjectPool<StayStillOrder> StayStillOrderPool { get; }

        /// <summary>
        ///     Pool for defend orders
        /// </summary>
        ObjectPool<DefendOrder> DefendOrderPool { get; }

        /// <summary>
        ///     Pool for resource thief orders
        /// </summary>
        ObjectPool<ResourceThiefOrder> ResourceThiefOrderPool { get; }

        // Decorator factory methods (pooling not implemented due to constructor constraints)
        /// <summary>
        ///     Creates a new TimedDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        TimedDecorator CreateTimedDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a new TargetTrackingDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        TargetTrackingDecorator CreateTargetTrackingDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a new GunTargetDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        GunTargetDecorator CreateGunTargetDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a new EnemyDetectionDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        EnemyDetectionDecorator CreateEnemyDetectionDecorator(IBaseOrder wrappedOrder);

        /// <summary>
        ///     Creates a new ConditionalDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        ConditionalDecorator CreateConditionalDecorator(IBaseOrder wrappedOrder);
    }
}

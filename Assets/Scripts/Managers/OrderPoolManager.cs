using Common;
using System;
using System.Collections.Generic;
using UnitParts.Interfaces;
using UnitParts.Orders;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace Managers
{
    [DefaultExecutionOrder(-50)]
    public class OrderPoolManager : MonoBehaviour, IOrderPoolManager
    {
        private void Awake()
        {
            // Register this instance in the service locator
            ServiceLocator.Register<IOrderPoolManager>(this);

            // Ensure this manager persists across scenes
            DontDestroyOnLoad(gameObject);
        }

        private void OnDestroy() =>
            // Unregister from service locator when destroyed
            ServiceLocator.Unregister<IOrderPoolManager>();

        // Order pools
        public ObjectPool<MoveOrder> MoverOrderPool { get; } = new();
        public ObjectPool<AttackMoveOrder> AttackMoveOrderPool { get; } = new();
        public ObjectPool<StayStillOrder> StayStillOrderPool { get; } = new();
        public ObjectPool<DefendOrder> DefendOrderPool { get; } = new();
        public ObjectPool<ResourceThiefOrder> ResourceThiefOrderPool { get; } = new();

        // Decorator factory methods (pooling not implemented due to constructor constraints)
        /// <summary>
        /// Creates a new TimedDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        public TimedDecorator CreateTimedDecorator(IBaseOrder wrappedOrder) => new TimedDecorator(wrappedOrder);

        /// <summary>
        /// Creates a new TargetTrackingDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        public TargetTrackingDecorator CreateTargetTrackingDecorator(IBaseOrder wrappedOrder) => new TargetTrackingDecorator(wrappedOrder);

        /// <summary>
        /// Creates a new GunTargetDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        public GunTargetDecorator CreateGunTargetDecorator(IBaseOrder wrappedOrder) => new GunTargetDecorator(wrappedOrder);

        /// <summary>
        /// Creates a new EnemyDetectionDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        public EnemyDetectionDecorator CreateEnemyDetectionDecorator(IBaseOrder wrappedOrder) => new EnemyDetectionDecorator(wrappedOrder);

        /// <summary>
        /// Creates a new ConditionalDecorator instance. Pooling not available due to constructor requirements.
        /// </summary>
        public ConditionalDecorator CreateConditionalDecorator(IBaseOrder wrappedOrder) => new ConditionalDecorator(wrappedOrder);
    }
}

[Serializable]
public class ObjectPool<T> where T : IBaseOrder, new()
{
    private readonly Queue<T> _pool = new();

    public T Get()
    {
        // If an instance is available, use it; otherwise create a new one.
        if (_pool.Count > 0)
        {
            return _pool.Dequeue();
        }

        return new T();
    }

    public void Return(T item) =>
        // Optionally, reset the item’s state here if needed.
        _pool.Enqueue(item);
}

